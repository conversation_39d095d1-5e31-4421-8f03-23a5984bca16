import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  ResponsiveContainer,
  Tooltip,
  Area,
  AreaChart,
} from "recharts";
import { Eye, Calculator, FileChartColumnIncreasing } from "lucide-react";
import Image from "next/image";
import icons from "@/public/icons";

// Type definitions
interface ChartDataPoint {
  price: number;
  day: number;
}

interface CustomActiveDotProps {
  cx?: number;
  cy?: number;
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    payload: ChartDataPoint;
  }>;
  label?: string | number;
}

type TimeframeType = "7D" | "1M" | "1Y";

// Generate realistic chart data
const generateChartData = (timeframe: TimeframeType): ChartDataPoint[] => {
  const basePrice: number = 1;
  const currentPrice: number = 11.25;
  const data: ChartDataPoint[] = [];

  let days: number;
  switch (timeframe) {
    case "7D":
      days = 7;
      break;
    case "1M":
      days = 30;
      break;
    case "1Y":
      days = 365;
      break;
    default:
      days = 120;
  }

  const totalGrowth: number = currentPrice / basePrice;
  const dailyGrowthRate: number = Math.pow(totalGrowth, 1 / days);

  for (let i = 0; i < days; i++) {
    const volatility: number =
      0.05 * Math.sin(i * 0.3) + (Math.random() - 0.5) * 0.03;
    const trendPrice: number = basePrice * Math.pow(dailyGrowthRate, i);
    const price: number = trendPrice * (1 + volatility);

    data.push({
      price: Math.max(price, basePrice * 0.8),
      day: i,
    });
  }

  return data;
};

const BalanceChart: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] =
    useState<TimeframeType>("1Y");
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [hoveredPoint, setHoveredPoint] = useState<ChartDataPoint | null>(null);
  const [currentPrice, setCurrentPrice] = useState<number>(11.25);

  useEffect(() => {
    const data: ChartDataPoint[] = generateChartData(selectedTimeframe);
    setChartData(data);
    if (data.length > 0) {
      setCurrentPrice(data[data.length - 1].price);
    }
  }, [selectedTimeframe]);

  const timeframes: TimeframeType[] = ["7D", "1M", "1Y"];
  const balance: number = 158255.25;

  const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      setHoveredPoint(data);
    }
    return null;
  };

  const CustomActiveDot: React.FC<CustomActiveDotProps> = (props) => {
    const { cx, cy } = props;
    return (
      <g>
        <circle
          cx={cx}
          cy={cy}
          r={10}
          fill="none"
          stroke="#10B981"
          strokeWidth={1}
          opacity={0.3}
        />
        <circle
          cx={cx}
          cy={cy}
          r={6}
          fill="none"
          stroke="#10B981"
          strokeWidth={1}
          opacity={0.5}
        />
        <circle
          cx={cx}
          cy={cy}
          r={3}
          fill="#10B981"
          stroke="#ffffff"
          strokeWidth={2}
        />
      </g>
    );
  };

  const handleTimeframeChange = (timeframe: TimeframeType): void => {
    setSelectedTimeframe(timeframe);
  };

  const handleMouseLeave = (): void => {
    setHoveredPoint(null);
  };

  return (
    <div className=" relative">
      {/* Balance Section */}
      <div className="flex flex-col text-center text-white gap-0.5">
        <div className="font-medium text-base ">Your Balance</div>
        <div className="text-5xl font-bold tracking-tight">
          ${balance.toLocaleString()}
        </div>
        <div className="flex items-center justify-center gap-1">
          <span className="text-base font-400">{balance.toLocaleString()}</span>
          <Image src={icons.logos.logoWhite} alt="logo" />
        </div>
      </div>

      {/* USDP Price Section */}
      {/* <div className="text-center">
        <div className="text-green-300 text-sm mb-2">USDP Price Today</div>
        <div className="text-4xl font-bold text-green-400 mb-1">
          $
          {hoveredPoint
            ? hoveredPoint.price.toFixed(2)
            : currentPrice.toFixed(2)}
        </div>
        <div className="text-green-300 text-sm">4 jun</div>
      </div> */}

      {/* Chart Container */}
      <div className="relative">
        {/* Chart */}
        <div className="h-40 relative mt-10">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={chartData}
              margin={{ left: 20, right: 0 }}
              onMouseLeave={handleMouseLeave}
            >
              <defs>
                <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#ACEDC9" stopOpacity={0.4} />
                  <stop offset="50%" stopColor="#ACEDC9" stopOpacity={0.2} />
                  <stop offset="100%" stopColor="#ACEDC9" stopOpacity={0} />
                </linearGradient>
                <filter id="lineGlow">
                  <feGaussianBlur stdDeviation="3" result="coloredBlur" />
                  <feMerge>
                    <feMergeNode in="coloredBlur" />
                    <feMergeNode in="SourceGraphic" />
                  </feMerge>
                </filter>
              </defs>

              <XAxis
                dataKey="day"
                axisLine={false}
                tickLine={false}
                tick={false}
              />
              <YAxis hide />

              <Tooltip content={<CustomTooltip />} cursor={false} />

              <Area
                type="monotone"
                dataKey="price"
                stroke="#ACEDC9"
                strokeWidth={3}
                fill="url(#areaGradient)"
                filter="url(#lineGlow)"
                dot={false}
                activeDot={<CustomActiveDot />}
              />
            </AreaChart>
          </ResponsiveContainer>

          {/* Chart Overlay Labels */}
          <div className="absolute inset-0 pointer-events-none">
            {/* $1 at bottom left with date */}
            <div className="absolute bottom-7 -left-2">
              <p className="text-xs text-white font-bold">23 may</p>
              <p className=" text-xl font-bold text-[#ACEDC9]">$1</p>
            </div>

            {/* Center logo with USDP price info */}
            <div className="flex flex-col gap-4 absolute top-[3.6rem] left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
              {/* USDP Price Today section above logo */}
              <div className="flex flex-col gap-0">
                <div className="text-white font-medium text-xs">
                  USDP Price Today
                </div>
                <div className="text-white text-lg font-bold">
                  $
                  {hoveredPoint
                    ? hoveredPoint.price.toFixed(2)
                    : currentPrice.toFixed(2)}
                </div>
                <div className="text-white/70 text-xs">4 jun</div>
              </div>

              {/* Logo at center */}
              <div className="flex justify-center">
                <Image
                  src={icons.logos.logoChart}
                  alt="USDP Logo"
                  width={25}
                  height={27}
                  className="opacity-100"
                />
              </div>
            </div>

            {/* Infinity symbol at top right */}
            <div className="absolute -top-7 -right-2 text-xl font-bold text-[#ACEDC9]">
              ∞
            </div>
          </div>

          <div className="w-full flex items-start justify-between text-white text-xs font-400">
            <div className="flex flex-col">
              <p className="">First Buy</p>
              <p className="">10,000 USDP</p>
            </div>
            <div className="flex flex-col justify-end gap-3">
              <div className="flex flex-col text-right">
                <p className="">Price</p>
                <p className="">projection</p>
              </div>
              <div className="flex items-center gap-2">
                {timeframes.map((tf: TimeframeType) => (
                  <button
                    key={tf}
                    onClick={han}
                    className="relative w-9 h-9 cursor-pointer transition-all duration-200 hover:scale-105"
                  >
                    <svg
                      width="36"
                      height="36"
                      viewBox="0 0 36 36"
                      className="absolute inset-0"
                    >
                      <polygon
                        points="18,2 31,10 31,26 18,34 5,26 5,10"
                        fill="none"
                        stroke={
                          selectedTimeframe === tf ? "#3C3C3C" : "#3C3C3C"
                        }
                        strokeWidth={selectedTimeframe === tf ? "2" : "2"}
                        opacity={selectedTimeframe === tf ? "1" : "0.6"}
                        className="transition-all duration-200 hover:opacity-80"
                      />
                    </svg>

                    <div className="absolute inset-0 flex items-center justify-center text-white font-medium text-xs">
                      {tf}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Profit Section */}
      <div className="mt-28 mb-6 flex justify-between">
        <div className="flex flex-col gap-0.5">
          <div className="text-white text-xs font-400  mb-0.5">
            Your Profit in 41 days
          </div>
          <div className="text-3xl font-bold text-[#ACEDC9]">$141,073.12</div>
          <div className="text-base text-[#ACEDC9] font-bold">+ 386.5%</div>
        </div>
        <div className="flex flex-col gap-0.5 text-right">
          <div className="text-white text-xs font-400 mb-0.5">
            Yesterday’s Profit
          </div>
          <div className="text-3xl font-bold text-white ">$873.12 </div>
          <div className="text-base text-[#ACEDC9] font-bold">+ 14.5%</div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <FileChartColumnIncreasing size={14} className="text-white" />
          <span className="text-white font-medium text-sm">Full Statistic</span>
        </div>
        <div className="flex items-center gap-1">
          <Calculator size={14} className="text-white" />
          <span className="text-white font-medium text-sm">
            Profit Calculator
          </span>
        </div>
      </div>
    </div>
  );
};

export default BalanceChart;
