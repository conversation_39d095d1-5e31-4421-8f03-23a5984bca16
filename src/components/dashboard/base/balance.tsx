"use client";
import React from "react";
import Button from "@/components/ui/button";
import useUserStore from "@/store/user.store";
import BalanceChart from "./Chart";

const Balance = () => {
  const { logout } = useUserStore();

  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div
          style={{
            boxShadow: "0px 1px 10px 0px #00000040",
            background:
              "linear-gradient(183.04deg, rgba(217, 217, 217, 0) 33.2%, rgba(255, 255, 255, 0.4) 97.48%)",
            mixBlendMode: "soft-light",
          }}
          className="absolute inset-0 rounded-xl"
        />

        <BalanceChart />
      </div>
      <Button
        type="button"
        style={{
          background: `radial-gradient(82.43% 811.25% at 12.97% -295%, #ACEDC9 0%, #2E7D51 100%),
        radial-gradient(75.6% 174.31% at 52.86% 26.04%, #FFFFFF 0%, #F9FBFD 100%)`,
        }}
        className=""
        onClick={() => {}}
      >
        <p className="text-2xl text-white font-bold">Buy USDP</p>
      </Button>
    </div>
  );
};

export default Balance;
