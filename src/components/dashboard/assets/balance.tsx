"use client";
import React from "react";
import Button from "@/components/ui/button";

const Balance = () => {
  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div className="flex flex-col text-center text-white gap-0.5">
          <div className="font-medium text-base ">Your Balance</div>
          <div className="text-5xl font-bold tracking-tight">
            ${(158255.25).toLocaleString()}
          </div>
        </div>{" "}
      </div>
      <Button
        type="button"
        style={{
          background: `radial-gradient(82.43% 811.25% at 12.97% -295%, #ACEDC9 0%, #2E7D51 100%),
        radial-gradient(75.6% 174.31% at 52.86% 26.04%, #FFFFFF 0%, #F9FBFD 100%)`,
        }}
        className=""
        onClick={() => {}}
      >
        <p className="text-2xl text-white font-bold">Buy USDP</p>
      </Button>
    </div>
  );
};

export default Balance;
